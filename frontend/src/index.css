/* Import accessibility and responsive styles */
@import './styles/accessibility.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for HTML summary content */
@layer components {
  .bill-summary-content {
    @apply text-gray-700 leading-relaxed;
  }

  .bill-summary-content h1,
  .bill-summary-content h2,
  .bill-summary-content h3,
  .bill-summary-content h4,
  .bill-summary-content h5,
  .bill-summary-content h6 {
    @apply font-bold text-gray-900 mt-4 mb-2;
  }

  .bill-summary-content h1 { @apply text-2xl; }
  .bill-summary-content h2 { @apply text-xl; }
  .bill-summary-content h3 { @apply text-lg; }
  .bill-summary-content h4 { @apply text-base; }

  .bill-summary-content p {
    @apply mb-3 text-gray-700;
  }

  .bill-summary-content strong {
    @apply font-semibold text-gray-900;
  }

  .bill-summary-content em {
    @apply italic text-gray-600;
  }

  .bill-summary-content ul,
  .bill-summary-content ol {
    @apply ml-4 mb-3;
  }

  .bill-summary-content li {
    @apply mb-1;
  }

  .bill-summary-content ul li {
    @apply list-disc;
  }

  .bill-summary-content ol li {
    @apply list-decimal;
  }
}
/* Mobile responsiveness utilities */
@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-4 {
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Ensure proper touch targets on mobile */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Safe area padding for mobile devices */
  .safe-area-padding {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Prevent horizontal scroll on mobile */
  .prevent-horizontal-scroll {
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }

  /* Mobile-first responsive containers */
  .container-responsive {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  @media (min-width: 640px) {
    .container-responsive {
      max-width: 640px;
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 768px) {
    .container-responsive {
      max-width: 768px;
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  @media (min-width: 1024px) {
    .container-responsive {
      max-width: 1024px;
    }
  }

  @media (min-width: 1280px) {
    .container-responsive {
      max-width: 1280px;
    }
  }

  /* Responsive text utilities */
  .text-responsive-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }

  .text-responsive-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .text-responsive-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  @media (min-width: 640px) {
    .text-responsive-xs {
      font-size: 0.875rem;
      line-height: 1.25rem;
    }

    .text-responsive-sm {
      font-size: 1rem;
      line-height: 1.5rem;
    }

    .text-responsive-base {
      font-size: 1.125rem;
      line-height: 1.75rem;
    }
  }

  /* Mobile-specific spacing */
  .space-mobile {
    padding: 1rem;
  }

  @media (min-width: 640px) {
    .space-mobile {
      padding: 1.5rem;
    }
  }

  @media (min-width: 768px) {
    .space-mobile {
      padding: 2rem;
    }
  }

  /* Responsive grid utilities */
  .grid-responsive-1 {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  @media (min-width: 640px) {
    .grid-responsive-1 {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .grid-responsive-1 {
      grid-template-columns: repeat(3, 1fr);
      gap: 2rem;
    }
  }

  /* Button responsive utilities */
  .btn-responsive {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.5rem;
    transition: all 0.2s;
  }

  @media (min-width: 640px) {
    .btn-responsive {
      padding: 0.875rem 1.5rem;
      font-size: 1rem;
    }
  }
}

/* Mobile-specific styles */
@media (max-width: 640px) {
  /* Ensure sidebar doesn't cause horizontal scroll */
  .sidebar-mobile {
    width: 100vw;
    max-width: 280px;
  }

  /* Adjust font sizes for better mobile readability */
  .mobile-text-adjust h1 {
    font-size: 1.5rem;
    line-height: 1.4;
  }

  .mobile-text-adjust h2 {
    font-size: 1.25rem;
    line-height: 1.4;
  }

  .mobile-text-adjust h3 {
    font-size: 1.125rem;
    line-height: 1.4;
  }

  /* Mobile form improvements */
  .form-mobile input,
  .form-mobile textarea,
  .form-mobile select {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.875rem;
    border-radius: 0.5rem;
    border: 1px solid #d1d5db;
    transition: all 0.2s;
  }

  .form-mobile input:focus,
  .form-mobile textarea:focus,
  .form-mobile select:focus {
    outline: none;
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
  }

  /* Mobile button improvements */
  .btn-mobile {
    min-height: 44px;
    padding: 0.875rem 1rem;
    font-size: 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Mobile card improvements */
  .card-mobile {
    margin: 0.5rem;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
  }

  /* Login page specific improvements */
  .login-container {
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile */
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 1rem;
  }

  /* Mobile navigation improvements */
  .nav-mobile {
    padding: 1rem;
  }

  /* Mobile modal improvements */
  .modal-mobile {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
    overflow-y: auto;
  }
}

/* Tablet-specific styles */
@media (min-width: 641px) and (max-width: 1023px) {
  .tablet-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .tablet-sidebar {
    width: 240px;
  }
}

/* Ensure proper spacing on very small screens */
@media (max-width: 375px) {
  .container-mobile {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .text-xs-mobile {
    font-size: 0.75rem;
  }

  .text-sm-mobile {
    font-size: 0.875rem;
  }

  .space-xs-mobile {
    padding: 0.5rem;
  }

  /* Login form specific improvements for small screens */
  .card-mobile {
    margin: 0.25rem;
    padding: 0.75rem !important;
  }

  .form-mobile input,
  .form-mobile textarea,
  .form-mobile select {
    padding: 0.75rem;
    font-size: 16px;
  }

  .btn-mobile {
    padding: 0.75rem 1rem;
    min-height: 44px;
  }

  /* Adjust mobile text for very small screens */
  .mobile-text-adjust h1 {
    font-size: 1.25rem;
    line-height: 1.3;
  }

  .mobile-text-adjust h2 {
    font-size: 1.125rem;
    line-height: 1.3;
  }
}

/* Large screen optimizations */
@media (min-width: 1280px) {
  .container-xl {
    max-width: 1280px;
    margin: 0 auto;
  }

  .grid-xl {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }
}